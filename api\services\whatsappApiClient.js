/**
 * Unified WhatsApp API Client
 * Industry Standard: Single source of truth for all WhatsApp API interactions
 */

const axios = require("axios");
const WhatsAppResponseTypes = require("../types/whatsapp-responses");

class WhatsAppApiClient {
  constructor() {
    this.BASE_URL = process.env.WHATSAPP_API_URL || "https://general-wa.foodyqueen.com";
    this.DEFAULT_TIMEOUT = 30000;

    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.BASE_URL,
      timeout: this.DEFAULT_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CanvaEditor-WhatsApp-Client/1.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[WhatsApp API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[WhatsApp API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for standardization
    this.client.interceptors.response.use(
      (response) => {
        return this.standardizeResponse(response);
      },
      (error) => {
        return Promise.reject(this.standardizeError(error));
      }
    );
  }

  /**
   * Standardize successful responses
   */
  standardizeResponse(response) {
    const originalData = response.data;

    // Don't standardize binary data (like QR code images)
    if (Buffer.isBuffer(originalData) || originalData instanceof ArrayBuffer) {
      return response;
    }

    // If already in standard format, return as-is
    if (originalData.success !== undefined && originalData.data !== undefined) {
      return response;
    }

    // Convert legacy formats to standard format
    const standardized = {
      success: true,
      data: originalData,
      message: originalData.message || "Operation completed successfully",
      timestamp: new Date().toISOString(),
      requestId: response.headers['x-request-id'] || null
    };

    response.data = standardized;
    return response;
  }

  /**
   * Standardize error responses
   */
  standardizeError(error) {
    const standardError = new Error();

    if (error.response) {
      // Server responded with error status
      const errorData = error.response.data;

      standardError.response = {
        ...error.response,
        data: {
          success: false,
          error: {
            code: errorData.code || `HTTP_${error.response.status}`,
            message: errorData.message || errorData.error || error.message,
            details: errorData.details || {}
          },
          timestamp: new Date().toISOString(),
          requestId: error.response.headers['x-request-id'] || null
        }
      };
    } else {
      // Network or other error
      standardError.message = error.message;
      standardError.code = error.code;
    }

    return standardError;
  }

  /**
   * Session Management APIs
   */
  async startSession(sessionId, options = {}) {
    const { timeout = 60000 } = options;

    try {
      const response = await this.client.get(`/session/start/${sessionId}`, {
        timeout
      });
      return response.data;
    } catch (error) {
      throw this.handleSessionError(error, 'start', sessionId);
    }
  }

  async getSessionStatus(sessionId, options = {}) {
    const { timeout = 30000 } = options;

    try {
      const response = await this.client.get(`/session/status/${sessionId}`, {
        timeout
      });

      // Ensure consistent status format
      const data = response.data.data || response.data;
      const standardStatus = {
        connected: data.connected || false,
        connectionStatus: data.connectionStatus || (data.connected ? 'connected' : 'disconnected'),
        canSendMessages: data.canSendMessages || false,
        user: data.user || null,
        lastChecked: new Date().toISOString(),
        sessionId: sessionId,
        qrRequired: !data.connected && !data.user
      };

      return {
        ...response.data,
        data: standardStatus
      };
    } catch (error) {
      throw this.handleSessionError(error, 'status', sessionId);
    }
  }

  async getQRCode(sessionId, options = {}) {
    const { timeout = 60000, format = 'image' } = options;

    try {
      const endpoint = format === 'data'
        ? `/session/qr/${sessionId}`
        : `/session/qr/${sessionId}/image`;

      const response = await this.client.get(endpoint, {
        timeout,
        responseType: format === 'image' ? 'arraybuffer' : 'json'
      });

      if (format === 'image') {
        // Check if response is actually binary data or JSON
        if (response.data instanceof ArrayBuffer || Buffer.isBuffer(response.data)) {
          // Binary image data
          return {
            success: true,
            data: {
              image: Buffer.from(response.data),
              contentType: response.headers['content-type'] || 'image/png'
            },
            message: "QR code image retrieved",
            timestamp: new Date().toISOString()
          };
        } else {
          // JSON response (session status, not image)
          return {
            success: false,
            data: response.data,
            message: response.data.message || "QR code not ready",
            timestamp: new Date().toISOString()
          };
        }
      }

      return response.data;
    } catch (error) {
      throw this.handleSessionError(error, 'qr', sessionId);
    }
  }

  async terminateSession(sessionId, options = {}) {
    const { timeout = 30000 } = options;

    try {
      const response = await this.client.delete(`/session/terminate/${sessionId}`, {
        timeout
      });
      return response.data;
    } catch (error) {
      throw this.handleSessionError(error, 'terminate', sessionId);
    }
  }

  async restartSession(sessionId, options = {}) {
    const { timeout = 60000 } = options;

    try {
      const response = await this.client.get(`/session/restart/${sessionId}`, {
        timeout
      });
      return response.data;
    } catch (error) {
      throw this.handleSessionError(error, 'restart', sessionId);
    }
  }

  /**
   * Message APIs
   */
  async sendMessage(sessionId, payload, options = {}) {
    const { timeout = 150000 } = options; // 2.5 minutes for message sending

    try {
      const response = await this.client.post(`/client/sendMessage/${sessionId}`, payload, {
        timeout
      });

      // Standardize message response
      const data = response.data.data || response.data;
      const standardResponse = {
        success: data.success || false,
        messageId: data.messageId || data.key?.id,
        timestamp: data.timestamp || data.messageTimestamp,
        to: payload.number,
        status: data.success ? 'sent' : 'failed'
      };

      return {
        ...response.data,
        data: standardResponse
      };
    } catch (error) {
      throw this.handleMessageError(error, sessionId, payload);
    }
  }

  /**
   * Error Handlers
   */
  handleSessionError(error, operation, sessionId) {
    const context = { operation, sessionId };

    if (error.response?.status === 404) {
      const notFoundError = new Error(`Session ${sessionId} not found`);
      notFoundError.code = 'SESSION_NOT_FOUND';
      notFoundError.context = context;
      return notFoundError;
    }

    if (error.response?.status === 409) {
      const conflictError = new Error(`Session ${sessionId} conflict`);
      conflictError.code = 'SESSION_CONFLICT';
      conflictError.context = context;
      return conflictError;
    }

    error.context = context;
    return error;
  }

  handleMessageError(error, sessionId, payload) {
    const context = { sessionId, to: payload.number };

    if (error.response?.data?.error) {
      const messageError = new Error(error.response.data.error);
      messageError.code = 'MESSAGE_SEND_FAILED';
      messageError.context = context;
      return messageError;
    }

    error.context = context;
    return error;
  }

  /**
   * Health Check
   */
  async healthCheck() {
    try {
      const response = await this.client.get('/health', {
        timeout: 10000
      });
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'HEALTH_CHECK_FAILED',
          message: error.message
        },
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
module.exports = new WhatsAppApiClient();