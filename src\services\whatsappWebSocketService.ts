import { io, Socket } from "socket.io-client";

export interface WhatsAppStatusData {
  status: string;
  connectionStatus: string;
  connected: boolean;
  lastChecked: Date;
  lastUpdated: Date;
  exists: boolean;
  isSessionActive: boolean;
  isReconnecting: boolean;
  reconnectionAttempts: number;
  user: any;
  error?: string;
}

export type WhatsAppConnectionState =
  | "disconnected"
  | "connecting"
  | "connected"
  | "error"
  | "checking";

class WhatsAppWebSocketService {
  private socket: Socket | null = null;
  private userId: string | null = null;
  private statusCallback: ((status: WhatsAppStatusData) => void) | null = null;
  private connectionCallback: ((connected: boolean) => void) | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  /**
   * Connect to WebSocket server
   */
  connect(userId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.socket && this.socket.connected) {
        console.log("WebSocket already connected");
        resolve(true);
        return;
      }

      this.userId = userId;

      try {
        this.socket = io("https://adstudioserver.foodyqueen.com", {
          transports: ["websocket", "polling"],
          timeout: 20000,
          reconnection: true,
          reconnectionAttempts: this.maxReconnectAttempts,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
        });

        this.socket.on("connect", () => {
          console.log("WebSocket connected successfully");
          this.reconnectAttempts = 0;

          // Join user room for targeted notifications
          console.log(
            `🔥 WEBSOCKET: Joining room for userId: ${userId} (room: user_${userId})`
          );
          this.socket!.emit("join", userId);

          if (this.connectionCallback) {
            this.connectionCallback(true);
          }

          resolve(true);
        });

        this.socket.on("disconnect", (reason) => {
          console.log("WebSocket disconnected:", reason);

          if (this.connectionCallback) {
            this.connectionCallback(false);
          }

          // Only attempt reconnection if it wasn't a manual disconnect
          if (reason !== "io client disconnect") {
            this.attemptReconnection();
          }
        });

        this.socket.on("connect_error", (error) => {
          console.error("WebSocket connection error:", error);

          if (this.connectionCallback) {
            this.connectionCallback(false);
          }

          reject(error);
        });

        this.socket.on(
          "whatsapp-status-change",
          (statusData: WhatsAppStatusData) => {
            console.log("Received WhatsApp status update:", statusData);

            if (this.statusCallback) {
              this.statusCallback(statusData);
            }
          }
        );

        // Set connection timeout
        setTimeout(() => {
          if (!this.socket || !this.socket.connected) {
            reject(new Error("WebSocket connection timeout"));
          }
        }, 10000);
      } catch (error) {
        console.error("Error creating WebSocket connection:", error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.userId = null;
    this.statusCallback = null;
    this.connectionCallback = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Set callback for WhatsApp status updates
   */
  onStatusChange(callback: (status: WhatsAppStatusData) => void): void {
    this.statusCallback = callback;
  }

  /**
   * Set callback for WebSocket connection status
   */
  onConnectionChange(callback: (connected: boolean) => void): void {
    this.connectionCallback = callback;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.socket ? this.socket.connected : false;
  }

  /**
   * Get current user ID
   */
  getUserId(): string | null {
    return this.userId;
  }

  /**
   * Attempt to reconnect to WebSocket
   */
  private attemptReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("Max reconnection attempts reached, giving up");
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);

    console.log(
      `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`
    );

    this.reconnectTimeout = setTimeout(() => {
      if (this.userId) {
        this.connect(this.userId).catch((error) => {
          console.error("Reconnection failed:", error);
          this.attemptReconnection();
        });
      }
    }, delay);
  }

  /**
   * Start monitoring WhatsApp status via backend
   */
  async startMonitoring(userId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `https://adstudioserver.foodyqueen.com/api/whatsapp-monitor-start/${userId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        console.log("WebSocket monitoring started successfully");
        return true;
      } else {
        console.error("Failed to start WebSocket monitoring:", data.message);
        return false;
      }
    } catch (error) {
      console.error("Error starting WebSocket monitoring:", error);
      return false;
    }
  }

  /**
   * Stop monitoring WhatsApp status via backend
   */
  async stopMonitoring(userId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `https://adstudioserver.foodyqueen.com/api/whatsapp-monitor-stop/${userId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        console.log("WebSocket monitoring stopped successfully");
        return true;
      } else {
        console.error("Failed to stop WebSocket monitoring:", data.message);
        return false;
      }
    } catch (error) {
      console.error("Error stopping WebSocket monitoring:", error);
      return false;
    }
  }

  /**
   * Get monitoring status from backend
   */
  async getMonitoringStatus(userId: string): Promise<any> {
    try {
      const response = await fetch(
        `https://adstudioserver.foodyqueen.com/api/whatsapp-monitor-status/${userId}`
      );
      const data = await response.json();

      if (data.success) {
        return data;
      } else {
        console.error("Failed to get monitoring status:", data.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting monitoring status:", error);
      return null;
    }
  }
}

// Create singleton instance
const whatsappWebSocketService = new WhatsAppWebSocketService();

export default whatsappWebSocketService;
