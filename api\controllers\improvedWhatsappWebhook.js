const SessionManager = require("../services/sessionManager");
const WhatsAppSettings = require("../models/whatsappSettings");

/**
 * Improved WhatsApp Status Webhook Handler
 * Uses SessionManager for clean session-to-user mapping
 */
const improvedWhatsappWebhook = async (req, res) => {
  try {
    console.log("Received WhatsApp status webhook:", req.body);

    const { sessionId, status, connected, user, connectionStatus, data } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        message: "Session ID is required",
        success: false,
      });
    }

    // Use SessionManager to get userId directly from sessionId
    const userId = await SessionManager.getUserIdFromSession(sessionId);

    if (!userId) {
      console.log(`Could not find userId for sessionId: ${sessionId}`);
      return res.status(404).json({
        message: "Session mapping not found",
        success: false,
      });
    }

    console.log(`Processing webhook for userId: ${userId}, sessionId: ${sessionId}, status: ${status}`);

    // Update session status in SessionManager
    const finalStatus = connected ? 'connected' : (status || 'disconnected');
    await SessionManager.updateSessionStatus(sessionId, finalStatus, {
      user: user,
      lastWebhookReceived: new Date(),
      connectionStatus: connectionStatus,
      data: data
    });

    // Update WhatsApp settings in database
    try {
      const settings = await WhatsAppSettings.findOne({ userId });
      if (settings) {
        settings.connectionStatus = finalStatus;
        settings.lastChecked = new Date();
        await settings.save();
        console.log(`Updated database status for user ${userId}: ${finalStatus}`);
      }
    } catch (dbError) {
      console.error("Error updating database:", dbError);
      // Don't fail the webhook for database errors
    }

    // Prepare status data for WebSocket emission
    const statusData = {
      sessionId,
      status: finalStatus,
      connectionStatus: finalStatus,
      connected: connected || false,
      user: user || null,
      lastChecked: new Date(),
      lastUpdated: new Date(),
      exists: true,
      isSessionActive: data?.isSessionActive || false,
      isReconnecting: data?.isReconnecting || false,
      reconnectionAttempts: data?.reconnectionAttempts || 0,
    };

    // Emit WebSocket event to user room
    if (global.io) {
      const roomName = `user_${userId}`;
      global.io.to(roomName).emit('whatsapp-status-change', statusData);
      console.log(`🔥 WEBHOOK: Sent WhatsApp status update via WebSocket to room: ${roomName}, status: ${finalStatus}`);
      console.log(`🔥 WEBHOOK: StatusData:`, JSON.stringify(statusData, null, 2));
    }

    // Stop monitoring if user is connected (webhook will handle updates)
    if (finalStatus === 'connected') {
      const whatsappWebSocketService = require("../services/whatsappWebSocketService");
      whatsappWebSocketService.stopMonitoring(userId);
      console.log(`User ${userId} connected via webhook, stopped polling monitoring`);
    }

    return res.status(200).json({
      message: "Webhook processed successfully",
      userId: userId,
      sessionId: sessionId,
      status: finalStatus,
      success: true,
    });

  } catch (error) {
    console.error("Error processing WhatsApp webhook:", error);
    return res.status(500).json({
      message: "Error processing webhook",
      error: error.message,
      success: false,
    });
  }
};

module.exports = improvedWhatsappWebhook;