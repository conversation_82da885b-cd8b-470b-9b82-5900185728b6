const whatsappApiClient = require("../services/whatsappApiClient");
const SessionManager = require("../services/sessionManager");

/**
 * Improved QR Code Handler
 * Ensures consistent response format and proper session management
 */
const improvedQRHandler = async (req, res) => {
  try {
    const { username } = req.params;

    if (!username) {
      return res.status(400).json({
        success: false,
        message: "Username is required",
      });
    }

    console.log("Fetching QR code for session:", username);

    // Get session info from SessionManager
    const session = await SessionManager.getSession(username);
    if (session) {
      await SessionManager.updateSessionStatus(username, 'connecting');
    }

    try {
      // Call the WhatsApp API to get the QR code
      const response = await whatsappApiClient.getQRCode(username, {
        timeout: 90000, // 90 second timeout
        format: 'image'
      });

      console.log("WhatsApp QR API response:", {
        success: response.success,
        dataType: typeof response.data,
        isBuffer: Buffer.isBuffer(response.data),
        hasImageProperty: !!(response.data && response.data.image),
        isImageBuffer: !!(response.data && Buffer.isBuffer(response.data.image)),
        dataLength: Buffer.isBuffer(response.data) ? response.data.length :
          (response.data && Buffer.isBuffer(response.data.image)) ? response.data.image.length : 0,
      });

      // Handle different response formats consistently
      let imageBuffer = null;
      let contentType = "image/png";

      if (Buffer.isBuffer(response.data)) {
        // Direct Buffer response
        imageBuffer = response.data;
      } else if (response.data && Buffer.isBuffer(response.data.image)) {
        // JSON response with embedded Buffer
        imageBuffer = response.data.image;
        contentType = response.data.contentType || "image/png";
      } else if (response.success && response.data && response.data.image && Buffer.isBuffer(response.data.image)) {
        // Standardized response format
        imageBuffer = response.data.image;
        contentType = response.data.contentType || "image/png";
      }

      if (imageBuffer) {
        // Convert Buffer to base64
        const base64Image = imageBuffer.toString("base64");

        return res.status(200).json({
          success: true,
          qr: base64Image,
          username: username,
          contentType: contentType,
          message: "QR code retrieved successfully",
          timestamp: new Date().toISOString()
        });
      }

      // Handle non-image responses (session status, errors, etc.)
      const responseData = response.data || response;

      if (responseData.connected === true || responseData.message?.includes("already connected")) {
        return res.status(200).json({
          success: false,
          qrReady: false,
          message: "Session already connected. No QR code needed.",
          username: username,
          connected: true,
          connectionStatus: "connected",
          user: responseData.user || null,
        });
      } else {
        return res.status(200).json({
          success: false,
          qrReady: false,
          message: responseData.message || response.message || "QR code is not ready yet",
          username: username,
          timeout: false,
        });
      }

    } catch (error) {
      console.error("Error fetching QR code:", error);

      // Handle timeout errors
      if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
        return res.status(200).json({
          success: false,
          qrReady: false,
          message: "QR code request timed out. Please try again.",
          username: username,
          timeout: true,
        });
      }

      // Handle other errors
      return res.status(200).json({
        success: false,
        qrReady: false,
        message: error.message || "Failed to fetch QR code",
        username: username,
        error: true,
      });
    }

  } catch (error) {
    console.error("Error in QR code handler:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = improvedQRHandler;