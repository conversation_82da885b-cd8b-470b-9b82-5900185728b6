/* eslint-disable no-undef */
/* eslint-disable @typescript-eslint/no-var-requires */
const axios = require("axios");
const WhatsAppSettings = require("../models/whatsappSettings");

const WHATSAPP_API = {
  BASE_URL: "https://general-wa.foodyqueen.com",
  CHECK_STATUS: (username) =>
    `${WHATSAPP_API.BASE_URL}/session/status/${username}`,
};

class WhatsAppWebSocketService {
  constructor() {
    this.statusChecks = new Map(); // Track ongoing status checks
    this.userConnections = new Map(); // Track user WebSocket connections
  }

  /**
   * Start monitoring WhatsApp status for a user
   * @param {string} userId - The user ID to monitor
   * @param {string} username - The WhatsApp username/session ID
   */
  startMonitoring(userId, username) {
    if (this.statusChecks.has(userId)) {
      console.log(`Already monitoring WhatsApp status for user ${userId}`);
      return;
    }

    console.log(`Starting WhatsApp status monitoring for user ${userId}`);

    const intervalId = setInterval(async () => {
      try {
        await this.checkAndNotifyStatus(userId, username);
      } catch (error) {
        console.error(`Error monitoring WhatsApp status for user ${userId}:`, error);
      }
    }, 2000); // Check every 2 seconds

    this.statusChecks.set(userId, {
      intervalId,
      username,
      lastStatus: null,
    });
  }

  /**
   * Stop monitoring WhatsApp status for a user
   * @param {string} userId - The user ID to stop monitoring
   */
  stopMonitoring(userId) {
    const statusCheck = this.statusChecks.get(userId);
    if (statusCheck) {
      clearInterval(statusCheck.intervalId);
      this.statusChecks.delete(userId);
      console.log(`Stopped WhatsApp status monitoring for user ${userId}`);
    }
  }

  /**
   * Check WhatsApp status and notify if changed
   * @param {string} userId - The user ID
   * @param {string} username - The WhatsApp username/session ID
   */
  async checkAndNotifyStatus(userId, username) {
    try {
      const response = await axios.get(WHATSAPP_API.CHECK_STATUS(username), {
        timeout: 30000, // 30 second timeout
      });

      const statusCheck = this.statusChecks.get(userId);
      if (!statusCheck) return;

      // Determine connection status
      let connectionStatus = "disconnected";
      let isConnected = false;
      let user = null;

      if (response.data) {
        if (response.data.connected === true) {
          connectionStatus = "connected";
          isConnected = true;
          user = response.data.user;
        } else if (response.data.connectionStatus) {
          connectionStatus = response.data.connectionStatus;
          isConnected = connectionStatus === "connected";
          user = response.data.user;
        } else if (response.data.success && response.data.state === "CONNECTED") {
          connectionStatus = "connected";
          isConnected = true;
          user = response.data.user;
        } else if (response.data.status === "CONNECTED") {
          connectionStatus = "connected";
          isConnected = true;
          user = response.data.user;
        } else if (
          response.data.status === "CONNECTING" ||
          response.data.state === "CONNECTING" ||
          response.data.connectionStatus === "connecting"
        ) {
          connectionStatus = "connecting";
        } else if (response.data.isReconnecting) {
          connectionStatus = "connecting";
        }
      }

      // Check if status has changed
      if (statusCheck.lastStatus !== connectionStatus) {
        console.log(`WhatsApp status changed for user ${userId}: ${statusCheck.lastStatus} -> ${connectionStatus}`);

        // Update last status
        statusCheck.lastStatus = connectionStatus;

        // Update database
        try {
          const settings = await WhatsAppSettings.findOne({ userId });
          if (settings) {
            settings.connectionStatus = connectionStatus;
            settings.lastChecked = new Date();
            await settings.save();
          }
        } catch (dbError) {
          console.error(`Error updating database for user ${userId}:`, dbError);
        }

        // Notify via WebSocket
        const statusData = {
          status: connectionStatus,
          connectionStatus: connectionStatus,
          connected: isConnected,
          lastChecked: new Date(),
          lastUpdated: new Date(),
          exists: true,
          isSessionActive: response.data.isSessionActive || false,
          isReconnecting: response.data.isReconnecting || false,
          reconnectionAttempts: response.data.reconnectionAttempts || 0,
          user: user,
        };

        this.notifyStatusChange(userId, statusData);

        // If connected, stop monitoring (user is logged in)
        if (connectionStatus === "connected") {
          console.log(`User ${userId} connected to WhatsApp, stopping monitoring`);
          this.stopMonitoring(userId);
        }
      }
    } catch (error) {
      console.error(`Error checking WhatsApp status for user ${userId}:`, error);

      // Notify about error
      const statusData = {
        status: "error",
        connectionStatus: "error",
        connected: false,
        lastChecked: new Date(),
        lastUpdated: new Date(),
        exists: true,
        isSessionActive: false,
        isReconnecting: false,
        reconnectionAttempts: 0,
        user: null,
        error: error.message,
      };

      this.notifyStatusChange(userId, statusData);
    }
  }

  /**
   * Notify user about status change via WebSocket
   * @param {string} userId - The user ID
   * @param {Object} statusData - The status data to send
   */
  notifyStatusChange(userId, statusData) {
    if (global.io) {
      global.io.to(`user_${userId}`).emit('whatsapp-status-change', statusData);
      console.log(`Sent WhatsApp status update to user ${userId}:`, statusData.status);
    }
  }

  /**
   * Get current monitoring status for a user
   * @param {string} userId - The user ID
   * @returns {Object|null} - The monitoring status or null if not monitoring
   */
  getMonitoringStatus(userId) {
    return this.statusChecks.get(userId) || null;
  }

  /**
   * Get all users currently being monitored
   * @returns {Array} - Array of user IDs being monitored
   */
  getAllMonitoredUsers() {
    return Array.from(this.statusChecks.keys());
  }

  /**
   * Clean up all monitoring
   */
  cleanup() {
    for (const userId of this.statusChecks.keys()) {
      this.stopMonitoring(userId);
    }
  }
}

// Create singleton instance
const whatsappWebSocketService = new WhatsAppWebSocketService();

module.exports = whatsappWebSocketService;
