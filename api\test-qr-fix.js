/**
 * Quick test to verify QR code fix
 */

const whatsappApiClient = require('./services/whatsappApiClient');

async function testQRCodeFix() {
  console.log('🧪 Testing QR code fix...');

  const testSessionId = 'test-session-' + Date.now();

  try {
    // Test QR code request
    console.log('📱 Requesting QR code for session:', testSessionId);

    const response = await whatsappApiClient.getQRCode(testSessionId, {
      format: 'image',
      timeout: 10000
    });

    console.log('✅ QR code request completed without error');
    console.log('📊 Response structure:', {
      success: response.success,
      hasData: !!response.data,
      hasImage: !!(response.data && response.data.image),
      message: response.message,
      timestamp: response.timestamp
    });

    if (response.success && response.data && response.data.image) {
      console.log('🖼️ Image data received:', {
        imageSize: response.data.image.length,
        contentType: response.data.contentType
      });
    } else {
      console.log('📄 Status response received:', {
        data: response.data,
        message: response.message
      });
    }

  } catch (error) {
    console.error('❌ Error during QR code test:', {
      code: error.code,
      message: error.message,
      context: error.context
    });

    // Check if it's the old error we were trying to fix
    if (error.code === 'ERR_INVALID_ARG_TYPE') {
      console.error('🚨 The original error still exists - fix not working');
      return false;
    } else {
      console.log('✅ Different error - the Buffer.from() fix is working');
    }
  }

  console.log('🎯 QR code fix test completed');
  return true;
}

// Run the test
testQRCodeFix()
  .then(success => {
    console.log(success ? '✅ Test passed' : '❌ Test failed');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });