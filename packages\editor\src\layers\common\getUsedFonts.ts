import { SerializedPage, FontData } from 'canva-editor/types';
import { isEqual, uniqWith } from 'lodash';
import { TextContentProps } from '..';

export const getUsedFonts = (data: SerializedPage[]) => {
    const fontList: FontData[] = [];
    data.forEach((page) => {
        Object.entries(page.layers).forEach(([, layer]) => {
            if (layer.type.resolvedName === 'TextLayer') {
                // Safety check: ensure fonts property exists and is an array
                const fonts = (layer.props as unknown as TextContentProps).fonts;
                if (fonts && Array.isArray(fonts)) {
                    fontList.push(...fonts);
                }
            }
        });
    });
    return uniqWith(fontList, isEqual);
};
