const SessionMapping = require('../models/SessionMapping');
const crypto = require('crypto');

class SessionManager {
  /**
   * Generate a clean, predictable session ID
   */
  static generateSessionId(userId) {
    return `wa_${userId}`;
  }

  /**
   * Generate a unique session ID with suffix if needed
   */
  static generateUniqueSessionId(userId) {
    const baseId = this.generateSessionId(userId);
    const timestamp = Date.now();
    return `${baseId}_${timestamp}`;
  }

  /**
   * Create a new session mapping
   */
  static async createSession(userId, options = {}) {
    try {
      // Cleanup old sessions for this user
      await SessionMapping.cleanupOldSessions(userId);

      // Check if user already has an active session
      const existingSession = await SessionMapping.findActiveSession(userId);
      if (existingSession) {
        console.log(`User ${userId} already has active session: ${existingSession.sessionId}`);
        return existingSession;
      }

      // Generate session ID
      const sessionId = options.unique ?
        this.generateUniqueSessionId(userId) :
        this.generateSessionId(userId);

      // Create new session mapping
      const sessionMapping = new SessionMapping({
        sessionId,
        userId,
        status: 'created',
        metadata: {
          userAgent: options.userAgent,
          ipAddress: options.ipAddress,
          deviceInfo: options.deviceInfo
        }
      });

      await sessionMapping.save();
      console.log(`Created session mapping: ${sessionId} → ${userId}`);

      return sessionMapping;
    } catch (error) {
      console.error('Error creating session mapping:', error);
      throw error;
    }
  }

  /**
   * Update session status
   */
  static async updateSessionStatus(sessionId, status, metadata = {}) {
    try {
      const session = await SessionMapping.findOneAndUpdate(
        { sessionId },
        {
          status,
          lastActivity: new Date(),
          $set: Object.keys(metadata).reduce((acc, key) => {
            acc[`metadata.${key}`] = metadata[key];
            return acc;
          }, {})
        },
        { new: true }
      );

      if (session) {
        console.log(`Updated session ${sessionId} status: ${status}`);
      } else {
        console.warn(`Session not found for update: ${sessionId}`);
      }

      return session;
    } catch (error) {
      console.error('Error updating session status:', error);
      throw error;
    }
  }

  /**
   * Get session by sessionId
   */
  static async getSession(sessionId) {
    try {
      return await SessionMapping.findOne({ sessionId });
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  /**
   * Get user ID from session ID
   */
  static async getUserIdFromSession(sessionId) {
    try {
      const session = await SessionMapping.findOne({ sessionId });
      return session ? session.userId : null;
    } catch (error) {
      console.error('Error getting userId from session:', error);
      return null;
    }
  }

  /**
   * Get all active sessions for a user
   */
  static async getUserSessions(userId) {
    try {
      return await SessionMapping.find({
        userId,
        status: { $in: ['created', 'connecting', 'connected'] }
      }).sort({ lastActivity: -1 });
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }

  /**
   * Terminate session
   */
  static async terminateSession(sessionId) {
    try {
      const session = await SessionMapping.findOneAndUpdate(
        { sessionId },
        {
          status: 'disconnected',
          lastActivity: new Date()
        },
        { new: true }
      );

      if (session) {
        console.log(`Terminated session: ${sessionId}`);
      }

      return session;
    } catch (error) {
      console.error('Error terminating session:', error);
      throw error;
    }
  }

  /**
   * Cleanup only error sessions (conservative approach)
   */
  static async cleanupErrorSessions() {
    try {
      // Only cleanup sessions that are in error state and old
      const result = await SessionMapping.deleteMany({
        status: 'error',
        lastActivity: { $lt: new Date(Date.now() - 3600000) } // 1 hour old error sessions
      });

      if (result.deletedCount > 0) {
        console.log(`Cleaned up ${result.deletedCount} error sessions`);
      }
      return result.deletedCount;
    } catch (error) {
      console.error('Error cleaning up error sessions:', error);
      return 0;
    }
  }

  /**
   * Cleanup expired sessions (only for manual use)
   */
  static async cleanupExpiredSessions() {
    try {
      // Only cleanup sessions that are truly old and disconnected
      const result = await SessionMapping.deleteMany({
        status: { $in: ['disconnected', 'error'] },
        lastActivity: { $lt: new Date(Date.now() - 86400000) } // 24 hours old
      });

      console.log(`Cleaned up ${result.deletedCount} expired sessions`);
      return result.deletedCount;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  /**
   * Get session statistics
   */
  static async getSessionStats() {
    try {
      const stats = await SessionMapping.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const total = await SessionMapping.countDocuments();

      return {
        total,
        byStatus: stats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Error getting session stats:', error);
      return { total: 0, byStatus: {} };
    }
  }
}

module.exports = SessionManager;