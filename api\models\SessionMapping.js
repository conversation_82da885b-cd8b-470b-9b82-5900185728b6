const mongoose = require('mongoose');

const sessionMappingSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: ['created', 'connecting', 'connected', 'disconnected', 'error'],
    default: 'created'
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 86400 // Auto-delete after 24 hours
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    deviceInfo: String
  }
});

// Compound index for efficient queries
sessionMappingSchema.index({ userId: 1, status: 1 });

// Update lastActivity on save
sessionMappingSchema.pre('save', function (next) {
  this.lastActivity = new Date();
  next();
});

// Static method to find active session for user
sessionMappingSchema.statics.findActiveSession = function (userId) {
  return this.findOne({
    userId,
    status: { $in: ['connecting', 'connected'] }
  }).sort({ lastActivity: -1 });
};

// Static method to cleanup old sessions
sessionMappingSchema.statics.cleanupOldSessions = function (userId) {
  return this.deleteMany({
    userId,
    status: { $in: ['disconnected', 'error'] },
    lastActivity: { $lt: new Date(Date.now() - 3600000) } // 1 hour old
  });
};

module.exports = mongoose.model('SessionMapping', sessionMappingSchema);