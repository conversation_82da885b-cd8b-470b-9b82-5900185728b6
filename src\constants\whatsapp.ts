// WhatsApp API endpoints and constants
// Industry Standard: Centralized API configuration with comprehensive endpoint coverage
export const WHATSAPP_API = {
  BASE_URL: "https://general-wa.foodyqueen.com",

  // Session Management Endpoints
  START_SESSION: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/start/${username}`,
  CHECK_STATUS: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/status/${username}`,
  RESTART_SESSION: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/restart/${username}`,
  TERMINATE_SESSION: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/terminate/${username}`,
  CLEAR_SESSION: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/clear/${username}`,
  ACTIVATE_SESSION: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/activate/${username}`,

  // QR Code Endpoints
  GET_QR_CODE: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/qr/${username}/image`,
  GET_QR_DATA: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/qr/${username}`,

  // Message Endpoints
  SEND_MESSAGE: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/client/sendMessage/${username}`,

  // Health & Monitoring
  HEALTH_CHECK: () => `${WHATSAPP_API.BASE_URL}/health`,
  SYSTEM_STATS: () => `${WHATSAPP_API.BASE_URL}/session/system-stats`,

  // Session Persistence & Management
  SET_PERSISTENCE: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/persistence/${username}`,
  GET_DETAILED_INFO: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/detailed/${username}`,

  // Conflict Resolution
  RESOLVE_CONFLICT: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/conflicts/${username}/resolve`,
  FORCE_TAKEOVER: (username: string) =>
    `${WHATSAPP_API.BASE_URL}/session/conflicts/${username}/force-takeover`,
} as const;

// WhatsApp connection states
export enum WhatsAppConnectionState {
  CHECKING = "checking",
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  ERROR = "error",
}

// QR code refresh interval in milliseconds (45 seconds)
export const QR_REFRESH_INTERVAL = 45000;
