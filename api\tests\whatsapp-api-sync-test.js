/**
 * WhatsApp API Synchronization Test Suite
 * Industry Standard: Comprehensive testing for API consistency and reliability
 */

const whatsappApiClient = require('../services/whatsappApiClient');
const WhatsAppResponseTypes = require('../types/whatsapp-responses');

describe('WhatsApp API Synchronization Tests', () => {
  const TEST_SESSION_ID = 'test-session-sync-' + Date.now();

  beforeAll(async () => {
    console.log('🧪 Starting WhatsApp API Synchronization Tests');
    console.log(`📱 Test Session ID: ${TEST_SESSION_ID}`);
  });

  afterAll(async () => {
    // Cleanup test session
    try {
      await whatsappApiClient.terminateSession(TEST_SESSION_ID);
      console.log('🧹 Test session cleaned up');
    } catch (error) {
      console.log('⚠️ Cleanup warning:', error.message);
    }
  });

  describe('1. Response Format Standardization', () => {
    test('Health check should return standardized format', async () => {
      const response = await whatsappApiClient.healthCheck();

      expect(response).toHaveProperty('success');
      expect(response).toHaveProperty('timestamp');

      if (response.success) {
        expect(response).toHaveProperty('data');
        expect(response).toHaveProperty('message');
      } else {
        expect(response).toHaveProperty('error');
        expect(response.error).toHaveProperty('code');
        expect(response.error).toHaveProperty('message');
      }
    });

    test('Session status should return standardized format', async () => {
      try {
        const response = await whatsappApiClient.getSessionStatus(TEST_SESSION_ID);

        // Should have standard response structure
        expect(response).toHaveProperty('success');
        expect(response).toHaveProperty('data');
        expect(response).toHaveProperty('message');
        expect(response).toHaveProperty('timestamp');

        // Data should have standardized session status fields
        expect(response.data).toHaveProperty('connected');
        expect(response.data).toHaveProperty('connectionStatus');
        expect(response.data).toHaveProperty('canSendMessages');
        expect(response.data).toHaveProperty('sessionId');
        expect(response.data).toHaveProperty('qrRequired');
        expect(response.data).toHaveProperty('lastChecked');

        // Connection status should be one of the standard values
        const validStatuses = ['disconnected', 'connecting', 'connected', 'error'];
        expect(validStatuses).toContain(response.data.connectionStatus);

      } catch (error) {
        // Should be standardized error
        expect(error).toHaveProperty('code');
        expect(error.code).toBe('SESSION_NOT_FOUND');
      }
    });
  });

  describe('2. Error Handling Standardization', () => {
    test('Non-existent session should return SESSION_NOT_FOUND error', async () => {
      const nonExistentSession = 'non-existent-session-' + Date.now();

      try {
        await whatsappApiClient.getSessionStatus(nonExistentSession);
        fail('Should have thrown an error for non-existent session');
      } catch (error) {
        expect(error.code).toBe('SESSION_NOT_FOUND');
        expect(error.context).toHaveProperty('sessionId', nonExistentSession);
        expect(error.context).toHaveProperty('operation', 'status');
      }
    });

    test('Invalid message payload should return proper error', async () => {
      try {
        await whatsappApiClient.sendMessage(TEST_SESSION_ID, {
          // Invalid payload - missing required fields
        });
        fail('Should have thrown an error for invalid payload');
      } catch (error) {
        expect(error.code).toBe('MESSAGE_SEND_FAILED');
        expect(error.context).toHaveProperty('sessionId', TEST_SESSION_ID);
      }
    });
  });

  describe('3. Timeout Management', () => {
    test('Session status should respect custom timeout', async () => {
      const startTime = Date.now();

      try {
        await whatsappApiClient.getSessionStatus(TEST_SESSION_ID, {
          timeout: 5000 // 5 second timeout
        });
      } catch (error) {
        const elapsed = Date.now() - startTime;

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          // Should timeout around 5 seconds, not default 30 seconds
          expect(elapsed).toBeLessThan(7000); // Allow 2 second buffer
          expect(elapsed).toBeGreaterThan(4000); // Should be at least 4 seconds
        }
      }
    });

    test('Message sending should use appropriate timeout', async () => {
      const startTime = Date.now();

      try {
        await whatsappApiClient.sendMessage(TEST_SESSION_ID, {
          number: '+1234567890',
          message: 'Test message'
        }, {
          timeout: 10000 // 10 second timeout
        });
      } catch (error) {
        const elapsed = Date.now() - startTime;

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          // Should timeout around 10 seconds, not default 150 seconds
          expect(elapsed).toBeLessThan(12000); // Allow 2 second buffer
          expect(elapsed).toBeGreaterThan(8000); // Should be at least 8 seconds
        }
      }
    });
  });

  describe('4. Request/Response Interceptors', () => {
    test('Requests should include proper headers', async () => {
      // This test verifies that the axios instance is configured with proper headers
      const client = whatsappApiClient.client;

      expect(client.defaults.headers['Content-Type']).toBe('application/json');
      expect(client.defaults.headers['User-Agent']).toBe('CanvaEditor-WhatsApp-Client/1.0');
      expect(client.defaults.baseURL).toBe('https://general-wa.foodyqueen.com');
      expect(client.defaults.timeout).toBe(30000);
    });

    test('Responses should be standardized by interceptors', async () => {
      // Mock a response that would need standardization
      const mockResponse = {
        data: {
          // Legacy format without success/data structure
          state: 'CONNECTED',
          message: 'session_connected'
        },
        headers: {}
      };

      const standardized = whatsappApiClient.standardizeResponse(mockResponse);

      expect(standardized.data).toHaveProperty('success', true);
      expect(standardized.data).toHaveProperty('data');
      expect(standardized.data).toHaveProperty('message');
      expect(standardized.data).toHaveProperty('timestamp');
    });
  });

  describe('5. Session Lifecycle Management', () => {
    test('Complete session lifecycle should work with standardized responses', async () => {
      let sessionResponse, qrResponse, statusResponse;

      try {
        // 1. Start session
        sessionResponse = await whatsappApiClient.startSession(TEST_SESSION_ID);
        expect(sessionResponse).toHaveProperty('success');
        expect(sessionResponse).toHaveProperty('timestamp');

        // 2. Get QR code (if session requires it)
        try {
          qrResponse = await whatsappApiClient.getQRCode(TEST_SESSION_ID, {
            format: 'data'
          });

          if (qrResponse.success) {
            expect(qrResponse.data).toHaveProperty('qr');
            expect(qrResponse.data).toHaveProperty('qrReady');
          }
        } catch (qrError) {
          // QR might not be available immediately
          console.log('QR code not immediately available:', qrError.message);
        }

        // 3. Check status
        statusResponse = await whatsappApiClient.getSessionStatus(TEST_SESSION_ID);
        expect(statusResponse).toHaveProperty('success');
        expect(statusResponse.data).toHaveProperty('connectionStatus');

        // 4. Terminate session
        const terminateResponse = await whatsappApiClient.terminateSession(TEST_SESSION_ID);
        expect(terminateResponse).toHaveProperty('success');

      } catch (error) {
        console.log('Session lifecycle test error:', error.message);
        // This is expected for test environment - the important thing is response format
        expect(error).toHaveProperty('code');
      }
    });
  });

  describe('6. Message Sending Standardization', () => {
    test('Message payload should be properly formatted', async () => {
      const testPayload = {
        number: '+1234567890',
        message: 'Test message for synchronization'
      };

      try {
        const response = await whatsappApiClient.sendMessage(TEST_SESSION_ID, testPayload);

        // Should return standardized message response
        expect(response).toHaveProperty('success');
        expect(response).toHaveProperty('data');

        if (response.success) {
          expect(response.data).toHaveProperty('messageId');
          expect(response.data).toHaveProperty('timestamp');
          expect(response.data).toHaveProperty('to');
          expect(response.data).toHaveProperty('status');
        }

      } catch (error) {
        // Should be standardized error
        expect(error).toHaveProperty('code');
        expect(error.code).toBe('MESSAGE_SEND_FAILED');
        expect(error.context).toHaveProperty('sessionId', TEST_SESSION_ID);
        expect(error.context).toHaveProperty('to', testPayload.number);
      }
    });

    test('Document message should be properly handled', async () => {
      const testPayload = {
        number: '+1234567890',
        message: 'Test document message',
        document: 'https://example.com/test-document.pdf'
      };

      try {
        const response = await whatsappApiClient.sendMessage(TEST_SESSION_ID, testPayload);

        if (response.success) {
          expect(response.data).toHaveProperty('messageId');
          expect(response.data).toHaveProperty('status');
        }

      } catch (error) {
        // Expected in test environment
        expect(error).toHaveProperty('code');
      }
    });
  });

  describe('7. Performance & Reliability', () => {
    test('Multiple concurrent requests should be handled properly', async () => {
      const concurrentRequests = Array(5).fill().map((_, index) =>
        whatsappApiClient.getSessionStatus(`concurrent-test-${index}`)
          .catch(error => ({ error: error.code }))
      );

      const results = await Promise.all(concurrentRequests);

      // All requests should complete (either success or standardized error)
      results.forEach(result => {
        if (result.error) {
          expect(result.error).toBe('SESSION_NOT_FOUND');
        } else {
          expect(result).toHaveProperty('success');
          expect(result).toHaveProperty('timestamp');
        }
      });
    });

    test('Request deduplication should work for identical requests', async () => {
      const startTime = Date.now();

      // Make identical requests simultaneously
      const identicalRequests = Array(3).fill().map(() =>
        whatsappApiClient.getSessionStatus(TEST_SESSION_ID)
          .catch(error => ({ error: error.code }))
      );

      const results = await Promise.all(identicalRequests);
      const endTime = Date.now();

      // Should complete quickly due to deduplication
      const elapsed = endTime - startTime;
      expect(elapsed).toBeLessThan(5000); // Should be much faster than 3 separate requests

      // All results should be identical
      const firstResult = results[0];
      results.forEach(result => {
        expect(result).toEqual(firstResult);
      });
    });
  });
});

// Integration test with campaign controller
describe('Campaign Controller Integration', () => {
  test('Campaign controller should use unified API client', () => {
    const campaignController = require('../controllers/campaignController');

    // Verify that campaign controller is using the unified client
    // This is a structural test to ensure the import is correct
    expect(campaignController).toBeDefined();

    // The actual functionality would be tested in campaign-specific tests
  });
});

// Performance benchmarks
describe('Performance Benchmarks', () => {
  test('API client initialization should be fast', () => {
    const startTime = Date.now();
    const client = require('../services/whatsappApiClient');
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(100); // Should initialize in under 100ms
    expect(client).toBeDefined();
    expect(client.client).toBeDefined();
  });

  test('Response standardization should be efficient', () => {
    const mockResponse = {
      data: {
        state: 'CONNECTED',
        message: 'session_connected',
        user: { id: '123', name: 'Test User' }
      },
      headers: { 'x-request-id': 'test-123' }
    };

    const startTime = Date.now();
    const standardized = whatsappApiClient.standardizeResponse(mockResponse);
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(10); // Should be very fast
    expect(standardized.data).toHaveProperty('success', true);
    expect(standardized.data).toHaveProperty('requestId', 'test-123');
  });
});

console.log('🧪 WhatsApp API Synchronization Test Suite Ready');
console.log('📋 Run with: npm test -- whatsapp-api-sync-test.js');
console.log('🎯 Tests cover: Response format, Error handling, Timeouts, Interceptors, Session lifecycle, Message sending, Performance');