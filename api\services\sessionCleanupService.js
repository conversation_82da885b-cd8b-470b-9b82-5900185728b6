const SessionManager = require('./sessionManager');

// Try to load node-cron, but don't fail if it's not available
let cron;
try {
  cron = require('node-cron');
} catch (error) {
  console.warn('node-cron not available, session cleanup will use manual triggers only');
}

class SessionCleanupService {
  constructor() {
    this.isRunning = false;
  }

  /**
   * Start the cleanup service with scheduled tasks
   */
  start() {
    if (this.isRunning) {
      console.log('Session cleanup service is already running');
      return;
    }

    console.log('Starting session cleanup service...');
    this.isRunning = true;

    if (cron) {
      // Run cleanup every hour
      this.hourlyCleanup = cron.schedule('0 * * * *', async () => {
        console.log('Running hourly session cleanup...');
        await this.cleanupExpiredSessions();
      });

      // Run stats report every 6 hours
      this.statsReport = cron.schedule('0 */6 * * *', async () => {
        console.log('Generating session statistics...');
        await this.logSessionStats();
      });

      // Run deep cleanup daily at 2 AM
      this.dailyCleanup = cron.schedule('0 2 * * *', async () => {
        console.log('Running daily deep cleanup...');
        await this.deepCleanup();
      });

      console.log('Session cleanup service started successfully with scheduled tasks');
    } else {
      console.log('Session cleanup service started in manual mode (node-cron not available)');
    }
  }

  /**
   * Stop the cleanup service
   */
  stop() {
    if (!this.isRunning) {
      console.log('Session cleanup service is not running');
      return;
    }

    console.log('Stopping session cleanup service...');

    if (this.hourlyCleanup) {
      this.hourlyCleanup.stop();
    }
    if (this.statsReport) {
      this.statsReport.stop();
    }
    if (this.dailyCleanup) {
      this.dailyCleanup.stop();
    }

    this.isRunning = false;
    console.log('Session cleanup service stopped');
  }

  /**
   * Clean up only error sessions (conservative approach)
   */
  async cleanupExpiredSessions() {
    try {
      // Only cleanup sessions that have connection errors, not time-based
      const count = await SessionManager.cleanupErrorSessions();
      if (count > 0) {
        console.log(`Cleaned up ${count} error sessions`);
      }
      return count;
    } catch (error) {
      console.error('Error during session cleanup:', error);
      return 0;
    }
  }

  /**
   * Log session statistics
   */
  async logSessionStats() {
    try {
      const stats = await SessionManager.getSessionStats();
      console.log('Session Statistics:', {
        total: stats.total,
        byStatus: stats.byStatus,
        timestamp: new Date().toISOString()
      });
      return stats;
    } catch (error) {
      console.error('Error getting session stats:', error);
      return null;
    }
  }

  /**
   * Deep cleanup - remove only truly orphaned sessions (very conservative)
   */
  async deepCleanup() {
    try {
      console.log('Starting deep cleanup...');

      // Only clean up sessions that are in error state AND very old (30 days)
      const SessionMapping = require('../models/SessionMapping');
      const result = await SessionMapping.deleteMany({
        status: 'error',
        createdAt: { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 30 days old
      });

      if (result.deletedCount > 0) {
        console.log(`Deep cleanup: Removed ${result.deletedCount} very old error sessions`);
      } else {
        console.log('Deep cleanup: No sessions needed cleanup');
      }

      // Log final stats
      await this.logSessionStats();

      return result.deletedCount;
    } catch (error) {
      console.error('Error during deep cleanup:', error);
      return 0;
    }
  }

  /**
   * Manual cleanup trigger
   */
  async runManualCleanup() {
    console.log('Running manual session cleanup...');
    const expired = await this.cleanupExpiredSessions();
    const deep = await this.deepCleanup();
    const stats = await this.logSessionStats();

    return {
      expiredCleaned: expired,
      deepCleaned: deep,
      currentStats: stats
    };
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      scheduledTasks: {
        hourlyCleanup: this.hourlyCleanup ? 'active' : 'inactive',
        statsReport: this.statsReport ? 'active' : 'inactive',
        dailyCleanup: this.dailyCleanup ? 'active' : 'inactive'
      }
    };
  }
}

// Create singleton instance
const sessionCleanupService = new SessionCleanupService();

module.exports = sessionCleanupService;