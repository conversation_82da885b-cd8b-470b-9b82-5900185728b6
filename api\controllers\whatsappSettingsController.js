/* eslint-disable no-undef */
/* eslint-disable @typescript-eslint/no-var-requires */
const WhatsAppSettings = require("../models/whatsappSettings");
const whatsappApiClient = require("../services/whatsappApiClient");
const whatsappWebSocketService = require("../services/whatsappWebSocketService");
const SessionManager = require("../services/sessionManager");

const whatsappSettingsController = {
  // Get WhatsApp settings for a user
  getWhatsAppSettings: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
        });
      }

      return res.status(200).json({
        message: "WhatsApp settings retrieved successfully",
        settings,
      });
    } catch (error) {
      console.error("Error retrieving WhatsApp settings:", error);
      return res.status(500).json({
        message: "Error retrieving WhatsApp settings",
        error: error.message,
      });
    }
  },

  // Create or update WhatsApp settings for a user
  saveWhatsAppSettings: async (req, res) => {
    try {
      const { userId, username } = req.body;

      if (!userId || !username) {
        return res.status(400).json({
          message: "User ID and WhatsApp username are required",
        });
      }

      // Find existing settings or create new ones
      let settings = await WhatsAppSettings.findOne({ userId });

      if (settings) {
        // Update existing settings
        settings.username = username;
        settings.updatedAt = new Date();
        await settings.save();
      } else {
        // Create new settings
        settings = new WhatsAppSettings({
          userId,
          username,
        });
        await settings.save();
      }

      return res.status(200).json({
        message: "WhatsApp settings saved successfully",
        settings,
      });
    } catch (error) {
      console.error("Error saving WhatsApp settings:", error);
      return res.status(500).json({
        message: "Error saving WhatsApp settings",
        error: error.message,
      });
    }
  },

  // Delete WhatsApp settings for a user
  deleteWhatsAppSettings: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find and delete WhatsApp settings for the user
      const deletedSettings = await WhatsAppSettings.findOneAndDelete({
        userId,
      });

      if (!deletedSettings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
        });
      }

      return res.status(200).json({
        message: "WhatsApp settings deleted successfully",
        deletedSettings,
      });
    } catch (error) {
      console.error("Error deleting WhatsApp settings:", error);
      return res.status(500).json({
        message: "Error deleting WhatsApp settings",
        error: error.message,
      });
    }
  },

  // Start a WhatsApp session
  startSession: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
          status: "disconnected",
        });
      }

      try {
        // Create session mapping with clean session ID
        const sessionMapping = await SessionManager.createSession(userId, {
          userAgent: req.headers['user-agent'],
          ipAddress: req.ip || req.connection.remoteAddress
        });

        console.log(`Starting WhatsApp session: ${sessionMapping.sessionId} for user: ${userId}`);

        // Call the WhatsApp API to start a session with clean session ID
        const response = await whatsappApiClient.startSession(sessionMapping.sessionId, {
          timeout: 60000, // 60 second timeout
        });

        // Update session status
        await SessionManager.updateSessionStatus(sessionMapping.sessionId, 'connecting');

        // Update the connection status to connecting
        settings.connectionStatus = "connecting";
        settings.username = sessionMapping.sessionId; // Update to use clean session ID
        settings.lastChecked = new Date();
        await settings.save();

        // Start WebSocket monitoring for this user
        whatsappWebSocketService.startMonitoring(userId, sessionMapping.sessionId);

        return res.status(200).json({
          message: "WhatsApp session started successfully",
          status: "connecting",
          sessionId: sessionMapping.sessionId,
          lastChecked: settings.lastChecked,
          success: true,
        });
      } catch (error) {
        console.error("Error starting WhatsApp session:", error);

        // Check if the error is because the session already exists
        if (
          error.response &&
          error.response.data &&
          error.response.data.error &&
          error.response.data.error.includes("Session already exists")
        ) {
          // Return 422 status to trigger frontend termination logic
          return res.status(422).json({
            message: "Session already exists",
            error: error.response.data.error,
            success: false,
            sessionExists: true,
          });
        }

        // This is a real error
        // Update the settings with error status
        settings.connectionStatus = "error";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "Error starting WhatsApp session",
          status: "error",
          lastChecked: settings.lastChecked,
          error: error.response?.data?.error || error.message,
          success: false,
        });
      }
    } catch (error) {
      console.error("Error in startSession:", error);
      return res.status(500).json({
        message: "Error starting WhatsApp session",
        error: error.message,
        success: false,
      });
    }
  },

  // Start WhatsApp session with custom session ID
  startSessionWithCustomId: async (req, res) => {
    try {
      const { userId } = req.params;
      const { sessionId } = req.body;

      if (!userId || !sessionId) {
        return res.status(400).json({
          message: "User ID and session ID are required",
        });
      }

      // Find existing settings or create new ones
      let settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        // Create new settings
        settings = new WhatsAppSettings({
          userId,
          username: sessionId,
        });
      } else {
        settings.username = sessionId;
      }

      await settings.save();

      try {
        // Call the WhatsApp API to start a session with custom ID
        const response = await whatsappApiClient.startSession(sessionId, {
          timeout: 90000,
        });
        console.log(response.data);
        // Update the connection status to connecting
        settings.connectionStatus = "connecting";
        settings.lastChecked = new Date();
        await settings.save();

        // Start WebSocket monitoring for this user
        whatsappWebSocketService.startMonitoring(userId, sessionId);

        return res.status(200).json({
          message: "WhatsApp session started successfully",
          status: "connecting",
          lastChecked: settings.lastChecked,
          success: true,
          sessionId: sessionId,
          data: response.data,
        });
      } catch (error) {
        console.error("Error starting WhatsApp session with custom ID:", error);

        // Check if the error is because the session already exists
        if (
          error.response &&
          error.response.data &&
          error.response.data.error &&
          error.response.data.error.includes("Session already exists")
        ) {
          // Return 422 status to trigger frontend termination logic
          return res.status(422).json({
            message: "Session already exists",
            error: error.response.data.error,
            success: false,
            sessionExists: true,
          });
        }

        // This is a real error
        // Update the settings with error status
        settings.connectionStatus = "error";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "Error starting WhatsApp session with custom ID",
          status: "error",
          lastChecked: settings.lastChecked,
          error: error.response?.data?.error || error.message,
          success: false,
        });
      }
    } catch (error) {
      console.error("Error in startSessionWithCustomId:", error);
      return res.status(500).json({
        message: "Error starting WhatsApp session",
        error: error.message,
        success: false,
      });
    }
  },

  // Check WhatsApp connection status
  checkConnectionStatus: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
          status: "disconnected",
          connectionStatus: "disconnected",
          connected: false,
          exists: false,
          isSessionActive: false,
          isReconnecting: false,
          reconnectionAttempts: 0,
          user: null,
          lastChecked: new Date(),
          lastUpdated: new Date(),
        });
      }

      try {
        // Call the WhatsApp API to check status
        const response = await whatsappApiClient.getSessionStatus(settings.username, {
          timeout: 60000, // 60 second timeout
        });
        console.log("WhatsApp status response:", response.data);

        // Handle the new response format from getSessionStatus
        let connectionStatus = "disconnected";
        let userData = null;
        let isSessionActive = false;
        let isReconnecting = false;
        let reconnectionAttempts = 0;

        if (response.data) {
          // Handle nested data structure: { success: true, data: { connected: true, ... } }
          const statusData = response.data.data || response.data;

          if (statusData.connected === true || response.data.connected === true) {
            // New nested format or direct format
            connectionStatus = "connected";
          } else if (statusData.connectionStatus || response.data.connectionStatus) {
            // Use the connectionStatus field from the response
            connectionStatus = statusData.connectionStatus || response.data.connectionStatus;
          } else if (response.data.success && response.data.state === "CONNECTED") {
            // Legacy response format: { success: true, state: "CONNECTED", message: "session_connected" }
            connectionStatus = "connected";
          } else if (response.data.status === "CONNECTED") {
            // Old response format with status property
            connectionStatus = "connected";
          } else if (
            response.data.status === "CONNECTING" ||
            response.data.state === "CONNECTING" ||
            statusData.connectionStatus === "connecting" ||
            response.data.connectionStatus === "connecting"
          ) {
            connectionStatus = "connecting";
          } else if (statusData.isReconnecting || response.data.isReconnecting) {
            // Handle reconnection state
            connectionStatus = "connecting";
          } else {
            connectionStatus = "disconnected";
          }

          // Extract additional data
          userData = statusData.user || response.data.user || null;
          isSessionActive = statusData.isSessionActive || response.data.isSessionActive || false;
          isReconnecting = statusData.isReconnecting || response.data.isReconnecting || false;
          reconnectionAttempts = statusData.reconnectionAttempts || response.data.reconnectionAttempts || 0;
        }

        // Update the settings with the new status
        settings.connectionStatus = connectionStatus;
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "WhatsApp connection status checked successfully",
          status: connectionStatus,
          connectionStatus: connectionStatus, // Include both for compatibility
          connected: connectionStatus === "connected",
          lastChecked: settings.lastChecked,
          lastUpdated: settings.lastChecked,
          exists: true,
          isSessionActive: isSessionActive,
          isReconnecting: isReconnecting,
          reconnectionAttempts: reconnectionAttempts,
          user: userData,
        });
      } catch (error) {
        // console.error("Error checking WhatsApp connection status:", error);

        // Update the settings with error status
        settings.connectionStatus = "error";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "Error checking WhatsApp connection status",
          status: "error",
          connectionStatus: "error",
          connected: false,
          lastChecked: settings.lastChecked,
          lastUpdated: settings.lastChecked,
          exists: true,
          isSessionActive: false,
          isReconnecting: false,
          reconnectionAttempts: 0,
          user: null,
          error: error.message,
        });
      }
    } catch (error) {
      console.error("Error in checkConnectionStatus:", error);
      return res.status(500).json({
        message: "Error checking WhatsApp connection status",
        error: error.message,
      });
    }
  },

  // Restart WhatsApp session
  restartSession: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
        });
      }

      try {
        // Call the WhatsApp API to restart session
        const response = await whatsappApiClient.restartSession(settings.username, {
          timeout: 60000, // 60 second timeout
        });

        // Update the connection status to connecting
        settings.connectionStatus = "connecting";
        settings.lastChecked = new Date();
        await settings.save();

        // Start WebSocket monitoring for this user
        whatsappWebSocketService.startMonitoring(userId, settings.username);

        return res.status(200).json({
          message: "WhatsApp session restarted successfully",
          status: "connecting",
          lastChecked: settings.lastChecked,
          success: true,
        });
      } catch (error) {
        console.error("Error restarting WhatsApp session:", error);

        // Update the settings with error status
        settings.connectionStatus = "error";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "Error restarting WhatsApp session",
          status: "error",
          lastChecked: settings.lastChecked,
          error: error.message,
          success: false,
        });
      }
    } catch (error) {
      console.error("Error in restartSession:", error);
      return res.status(500).json({
        message: "Error restarting WhatsApp session",
        error: error.message,
        success: false,
      });
    }
  },

  // Terminate WhatsApp session
  terminateSession: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
        });
      }

      // Stop WebSocket monitoring for this user
      whatsappWebSocketService.stopMonitoring(userId);

      try {
        // Call the WhatsApp API to terminate session
        const response = await whatsappApiClient.terminateSession(settings.username, {
          timeout: 60000, // 60 second timeout
        });

        // Update the connection status to disconnected
        settings.connectionStatus = "disconnected";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "WhatsApp session terminated successfully",
          status: "disconnected",
          lastChecked: settings.lastChecked,
          success: true,
        });
      } catch (error) {
        console.error("Error terminating WhatsApp session:", error);

        // Update the settings with error status
        settings.connectionStatus = "error";
        settings.lastChecked = new Date();
        await settings.save();

        return res.status(200).json({
          message: "Error terminating WhatsApp session",
          status: "error",
          lastChecked: settings.lastChecked,
          error: error.message,
          success: false,
        });
      }
    } catch (error) {
      console.error("Error in terminateSession:", error);
      return res.status(500).json({
        message: "Error terminating WhatsApp session",
        error: error.message,
        success: false,
      });
    }
  },

  // Get WhatsApp QR code data as base64
  getQRData: async (req, res) => {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          message: "Session ID is required",
        });
      }

      try {
        console.log("Fetching QR data for session:", sessionId);
        // Call the WhatsApp API to get the QR code data (not image)
        const response = await whatsappApiClient.getQRCode(sessionId, {
          timeout: 90000, // Increased timeout to 90 seconds to align with WhatsApp automation improvements
          format: 'data'
        });

        // Standardize response format
        if (response.success && response.data && response.data.qr) {
          response.qr = response.data.qr;
        }

        console.log("WhatsApp QR API response:", {
          status: response.status,
          data: response.data,
        });

        // Check if the response contains QR data
        if (response.data && response.data.success && response.data.qr) {
          // QR data is available
          return res.status(200).json({
            success: true,
            qr: response.data.qr, // This should be the QR data (base64 or data URL)
            sessionId: sessionId,
            contentType: "image/png", // QR codes are typically PNG
            message: "QR code data retrieved successfully",
          });
        } else {
          // QR is not ready - this is normal, not an error
          const message =
            response.data?.message || "QR code is not ready yet";
          return res.status(200).json({
            success: false,
            qrReady: false,
            message: message,
            sessionId: sessionId,
            timeout: response.data?.timeout || false,
          });
        }
      } catch (error) {
        console.error("Error fetching QR data:", error);

        // Handle timeout errors (common during QR generation)
        if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
          return res.status(200).json({
            success: false,
            qrReady: false,
            message: "QR code generation is taking longer than expected",
            sessionId: sessionId,
            timeout: true,
          });
        }

        // Handle 404 errors (session not found)
        if (error.response && error.response.status === 404) {
          return res.status(404).json({
            success: false,
            message: "Session not found",
            sessionId: sessionId,
          });
        }

        // Return error response
        return res.status(500).json({
          success: false,
          message: "Error getting WhatsApp QR code data",
          error: error.message,
          sessionId: sessionId,
        });
      }
    } catch (error) {
      console.error("Error in getQRData:", error);
      return res.status(500).json({
        success: false,
        message: "Error getting WhatsApp QR code data",
        error: error.message,
      });
    }
  },

  // Start WebSocket monitoring for WhatsApp status
  startWebSocketMonitoring: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Find WhatsApp settings for the user
      const settings = await WhatsAppSettings.findOne({ userId });

      if (!settings) {
        return res.status(404).json({
          message: "WhatsApp settings not found for this user",
        });
      }

      // Start monitoring
      whatsappWebSocketService.startMonitoring(userId, settings.username);

      return res.status(200).json({
        message: "WebSocket monitoring started successfully",
        userId: userId,
        username: settings.username,
        success: true,
      });
    } catch (error) {
      console.error("Error starting WebSocket monitoring:", error);
      return res.status(500).json({
        message: "Error starting WebSocket monitoring",
        error: error.message,
        success: false,
      });
    }
  },

  // Stop WebSocket monitoring for WhatsApp status
  stopWebSocketMonitoring: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      // Stop monitoring
      whatsappWebSocketService.stopMonitoring(userId);

      return res.status(200).json({
        message: "WebSocket monitoring stopped successfully",
        userId: userId,
        success: true,
      });
    } catch (error) {
      console.error("Error stopping WebSocket monitoring:", error);
      return res.status(500).json({
        message: "Error stopping WebSocket monitoring",
        error: error.message,
        success: false,
      });
    }
  },

  // Get WebSocket monitoring status
  getWebSocketMonitoringStatus: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          message: "User ID is required",
        });
      }

      const status = whatsappWebSocketService.getMonitoringStatus(userId);
      const allMonitored = whatsappWebSocketService.getAllMonitoredUsers();

      return res.status(200).json({
        message: "WebSocket monitoring status retrieved successfully",
        userId: userId,
        isMonitoring: !!status,
        monitoringData: status,
        allMonitoredUsers: allMonitored,
        success: true,
      });
    } catch (error) {
      console.error("Error getting WebSocket monitoring status:", error);
      return res.status(500).json({
        message: "Error getting WebSocket monitoring status",
        error: error.message,
        success: false,
      });
    }
  },

  // Webhook endpoint for WhatsApp status updates
  whatsappStatusWebhook: async (req, res) => {
    try {
      console.log("Received WhatsApp status webhook:", req.body);

      const { sessionId, status, connected, user, connectionStatus, data } = req.body;

      if (!sessionId) {
        return res.status(400).json({
          message: "Session ID is required",
          success: false,
        });
      }

      // Use SessionManager to get userId directly from sessionId
      const userId = await SessionManager.getUserIdFromSession(sessionId);

      if (!userId) {
        console.log(`Could not find userId for sessionId: ${sessionId}`);
        return res.status(404).json({
          message: "Session mapping not found",
          success: false,
        });
      }

      console.log(`Processing webhook for userId: ${userId}, sessionId: ${sessionId}, status: ${status}`);

      // Update session status in SessionManager
      const finalStatus = connected ? 'connected' : (status || 'disconnected');
      await SessionManager.updateSessionStatus(sessionId, finalStatus, {
        user: user,
        lastWebhookReceived: new Date(),
        connectionStatus: connectionStatus,
        data: data
      });

      // Update WhatsApp settings in database
      try {
        const settings = await WhatsAppSettings.findOne({ userId });
        if (settings) {
          settings.connectionStatus = finalStatus;
          settings.lastChecked = new Date();
          await settings.save();
          console.log(`Updated database status for user ${userId}: ${finalStatus}`);
        }
      } catch (dbError) {
        console.error("Error updating database:", dbError);
        // Don't fail the webhook for database errors
      }

      // Prepare status data for WebSocket emission
      const statusData = {
        sessionId,
        status: finalStatus,
        connectionStatus: finalStatus,
        connected: connected || false,
        user: user || null,
        lastChecked: new Date(),
        lastUpdated: new Date(),
        exists: true,
        isSessionActive: data?.isSessionActive || false,
        isReconnecting: data?.isReconnecting || false,
        reconnectionAttempts: data?.reconnectionAttempts || 0,
      };

      // Emit WebSocket event to user room
      if (global.io) {
        global.io.to(`user_${userId}`).emit('whatsapp-status-change', statusData);
        console.log(`Sent WhatsApp status update via WebSocket to user ${userId}: ${finalStatus}`);
      }

      // Stop monitoring if user is connected (webhook will handle updates)
      if (finalStatus === 'connected') {
        whatsappWebSocketService.stopMonitoring(userId);
        console.log(`User ${userId} connected via webhook, stopped polling monitoring`);
      }

      return res.status(200).json({
        message: "Webhook processed successfully",
        userId: userId,
        sessionId: sessionId,
        status: finalStatus,
        success: true,
      });

    } catch (error) {
      console.error("Error processing WhatsApp webhook:", error);
      return res.status(500).json({
        message: "Error processing webhook",
        error: error.message,
        success: false,
      });
    }
  },
};

module.exports = whatsappSettingsController;