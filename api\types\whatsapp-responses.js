/**
 * Standardized WhatsApp API Response Types
 * Industry Standard: Consistent response format across all endpoints
 */

const WhatsAppResponseTypes = {
  // Standard success response
  SUCCESS_RESPONSE: {
    success: true,
    data: {
      // Actual response data
    },
    message: "Operation completed successfully",
    timestamp: new Date().toISOString(),
    requestId: null // For request tracking
  },

  // Standard error response
  ERROR_RESPONSE: {
    success: false,
    error: {
      code: "ERROR_CODE",
      message: "Human readable error message",
      details: {} // Additional error context
    },
    timestamp: new Date().toISOString(),
    requestId: null
  },

  // Session status response
  SESSION_STATUS_RESPONSE: {
    success: true,
    data: {
      connected: false,
      connectionStatus: "disconnected", // disconnected | connecting | connected | error
      canSendMessages: false,
      user: null,
      lastChecked: new Date().toISOString(),
      sessionId: null,
      qrRequired: false
    },
    message: "Session status retrieved",
    timestamp: new Date().toISOString()
  },

  // QR Code response
  QR_CODE_RESPONSE: {
    success: true,
    data: {
      qr: "base64_qr_code_data",
      qrReady: true,
      contentType: "image/png",
      expiresAt: new Date(Date.now() + 45000).toISOString() // 45 seconds
    },
    message: "QR code generated successfully",
    timestamp: new Date().toISOString()
  }
};

module.exports = WhatsAppResponseTypes;