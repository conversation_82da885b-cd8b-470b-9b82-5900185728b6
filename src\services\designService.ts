/**
 * Enhanced Design Service with idempotent operations and proper error handling
 * Implements industry-standard practices for design creation and management
 */

import axios, { AxiosResponse } from "axios";
import { toast } from "sonner";
import {
  generateObjectId,
  generateRequestId,
  designCreationTracker,
  requestDeduplicator,
} from "../utils/idGenerator";

// API Configuration
const API_BASE_URL = "https://adstudioserver.foodyqueen.com/api";
const TEMPLATES_ENDPOINT = `${API_BASE_URL}/templates`;

// Types
export interface CreateDesignRequest {
  templateName: string;
  templateDesc?: string;
  userId: string;
  packedData?: any[];
  previewImage?: string;
  tags?: string[];
  isPublic?: boolean;
  isKiosk?: boolean;
  isCoupon?: boolean;
  width?: number;
  height?: number;
  backgroundColor?: string;
}

export interface CreateDesignResponse {
  message: string;
  template: {
    id: string;
    title: string;
    templateUrl?: string;
    thumbnailUrl?: string;
  };
}

export interface DesignCreationOptions {
  preventDuplicates?: boolean;
  showLoadingToast?: boolean;
  retryAttempts?: number;
  timeoutMs?: number;
}

/**
 * Enhanced Design Service Class
 */
class DesignService {
  private readonly defaultOptions: Required<DesignCreationOptions> = {
    preventDuplicates: true,
    showLoadingToast: true,
    retryAttempts: 3,
    timeoutMs: 30000, // 30 seconds
  };

  /**
   * Creates a new design with idempotent operation
   * Prevents duplicate creation and implements proper error handling
   */
  async createDesign(
    request: CreateDesignRequest,
    options: DesignCreationOptions = {}
  ): Promise<CreateDesignResponse> {
    const opts = { ...this.defaultOptions, ...options };

    // Generate unique design ID upfront
    const designId = generateObjectId();
    const requestId = generateRequestId();

    console.log(`🆕 Creating design with pre-generated ID: ${designId}`);
    console.log(`📋 Request ID: ${requestId}`);
    console.log(`📊 Request data:`, request);

    // Show loading toast if requested
    let toastId: string | number | undefined;
    if (opts.showLoadingToast) {
      toastId = toast.loading(`Creating "${request.templateName}"...`, {
        description: "Setting up your new design",
        duration: opts.timeoutMs,
      });
    }

    try {
      // Create default empty template data if not provided
      const defaultTemplateData = this.createDefaultTemplateData(
        request.width || 1080,
        request.height || 1080,
        request.backgroundColor || "rgb(255, 255, 255)",
        request.isCoupon || false
      );

      // Generate default thumbnail if not provided
      const defaultThumbnail = this.generateDefaultThumbnail(
        request.width || 1080,
        request.height || 1080,
        request.backgroundColor || "rgb(255, 255, 255)"
      );

      // Prepare the API request with proper template data
      const apiRequest = {
        ...request,
        _id: designId, // Include pre-generated ID
        requestId, // Include request ID for idempotency
        isPublic: false, // Always false for user designs
        packedData:
          request.packedData && request.packedData.length > 0
            ? request.packedData
            : [defaultTemplateData], // Use default template if none provided
        previewImage: request.previewImage || defaultThumbnail, // Use default thumbnail if none provided
        templateName: request.templateName, // Ensure template name is included
      };

      console.log(`📊 Final API request data:`, {
        ...apiRequest,
        previewImage: apiRequest.previewImage
          ? `${apiRequest.previewImage.substring(0, 50)}...`
          : "none",
        packedData: `Array with ${apiRequest.packedData?.length || 0} items`,
      });

      // Create the request function
      const createRequestFn = async (): Promise<CreateDesignResponse> => {
        console.log(`🚀 Making API call to create design...`);

        const response: AxiosResponse<CreateDesignResponse> = await axios.post(
          TEMPLATES_ENDPOINT,
          apiRequest,
          {
            timeout: opts.timeoutMs,
            headers: {
              "Content-Type": "application/json",
              "X-Request-ID": requestId, // Idempotency header
              "X-Design-ID": designId, // Design ID header
            },
          }
        );

        console.log(`✅ Design creation API response:`, response.data);
        return response.data;
      };

      // Use enhanced deduplication if enabled
      let result: CreateDesignResponse;
      if (opts.preventDuplicates) {
        // Use enhanced request deduplication with user and design context
        const deduplicationKey = `create-design-${designId}`;
        result = await requestDeduplicator.deduplicate(
          deduplicationKey,
          createRequestFn,
          {
            userId: request.userId,
            designId: designId,
            timeoutMs: opts.timeoutMs,
          }
        );
      } else {
        result = await createRequestFn();
      }

      // Dismiss loading toast and show success
      if (toastId) {
        toast.dismiss(toastId);
        toast.success(`"${request.templateName}" created successfully!`, {
          description: "Your design is ready to edit",
          duration: 3000,
        });
      }

      // Ensure the response includes our pre-generated ID
      if (result.template && !result.template.id) {
        result.template.id = designId;
      }

      console.log(
        `✅ Design created successfully with ID: ${result.template.id}`
      );
      return result;
    } catch (error: any) {
      console.error(`❌ Error creating design:`, error);

      // Dismiss loading toast
      if (toastId) {
        toast.dismiss(toastId);
      }

      // Handle specific error cases
      if (error.response?.status === 409) {
        // Conflict - design already exists
        toast.error("Design already exists", {
          description:
            "A design with this ID already exists. Please try again.",
          duration: 5000,
        });
        throw new Error(`Design with ID ${designId} already exists`);
      } else if (error.response?.status === 400) {
        // Bad request
        toast.error("Invalid design data", {
          description:
            error.response.data?.message ||
            "Please check your input and try again.",
          duration: 5000,
        });
        throw new Error(`Invalid design data: ${error.response.data?.message}`);
      } else if (error.code === "ECONNABORTED") {
        // Timeout
        toast.error("Request timed out", {
          description: "The server took too long to respond. Please try again.",
          duration: 5000,
        });
        throw new Error("Design creation timed out");
      } else if (!navigator.onLine) {
        // Offline
        toast.error("No internet connection", {
          description: "Please check your connection and try again.",
          duration: 5000,
        });
        throw new Error("No internet connection");
      } else {
        // Generic error
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          "Unknown error occurred";
        toast.error("Failed to create design", {
          description: errorMessage,
          duration: 5000,
        });
        throw new Error(`Failed to create design: ${errorMessage}`);
      }
    }
  }

  /**
   * Creates a design with retry logic
   */
  async createDesignWithRetry(
    request: CreateDesignRequest,
    options: DesignCreationOptions = {}
  ): Promise<CreateDesignResponse> {
    const opts = { ...this.defaultOptions, ...options };
    let lastError: Error;

    for (let attempt = 1; attempt <= opts.retryAttempts; attempt++) {
      try {
        console.log(
          `🔄 Design creation attempt ${attempt}/${opts.retryAttempts}`
        );

        // Only show loading toast on first attempt
        const attemptOptions = {
          ...opts,
          showLoadingToast: attempt === 1,
        };

        return await this.createDesign(request, attemptOptions);
      } catch (error: any) {
        lastError = error;

        // Don't retry for certain errors
        if (
          error.message.includes("already exists") ||
          error.response?.status === 400 ||
          error.response?.status === 409
        ) {
          throw error;
        }

        // Wait before retry (exponential backoff)
        if (attempt < opts.retryAttempts) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Checks if a design creation is in progress
   */
  isCreationInProgress(designId: string): boolean {
    return designCreationTracker.isCreationInProgress(designId);
  }

  /**
   * Gets all pending design creation requests
   */
  getPendingCreations(): string[] {
    return designCreationTracker.getPendingRequests();
  }

  /**
   * Clears tracking for a specific design
   */
  clearDesignTracking(designId: string): void {
    designCreationTracker.clearTracking(designId);
  }

  /**
   * Creates default template data for new designs
   */
  private createDefaultTemplateData(
    width: number,
    height: number,
    backgroundColor: string,
    isCoupon: boolean
  ): any {
    const templateId = generateObjectId();

    if (isCoupon) {
      // Complete coupon template structure - return empty array to force fallback to createEmptyTemplate
      // This ensures the complete coupon template from NewEditor.tsx is used
      return [];
    } else {
      // Default design template structure
      return {
        name: "",
        notes: "",
        layers: {
          ROOT: {
            type: { resolvedName: "RootLayer" },
            props: {
              boxSize: { width, height },
              position: { x: 0, y: 0 },
              rotate: 0,
              color: backgroundColor,
              image: null,
              gradientBackground: null,
            },
            locked: false,
            child: [],
            parent: null,
          },
        },
      };
    }
  }

  /**
   * Generates a default thumbnail for new designs
   */
  private generateDefaultThumbnail(
    width: number,
    height: number,
    backgroundColor: string
  ): string {
    // Create a simple base64 encoded 1x1 pixel PNG with the background color
    // This is a minimal valid PNG that will satisfy the thumbnail requirement

    // For now, return a simple base64 encoded transparent PNG
    // This is a 1x1 transparent PNG in base64
    const transparentPng =
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

    // If we can access canvas (browser environment), create a proper thumbnail
    if (typeof document !== "undefined") {
      try {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Set thumbnail size (maintain aspect ratio, max 300x300)
        const maxSize = 300;
        const aspectRatio = width / height;
        let thumbWidth, thumbHeight;

        if (aspectRatio > 1) {
          thumbWidth = maxSize;
          thumbHeight = maxSize / aspectRatio;
        } else {
          thumbHeight = maxSize;
          thumbWidth = maxSize * aspectRatio;
        }

        canvas.width = thumbWidth;
        canvas.height = thumbHeight;

        if (ctx) {
          // Fill with background color
          ctx.fillStyle = backgroundColor;
          ctx.fillRect(0, 0, thumbWidth, thumbHeight);

          // Add a subtle border
          ctx.strokeStyle = "#e0e0e0";
          ctx.lineWidth = 2;
          ctx.strokeRect(1, 1, thumbWidth - 2, thumbHeight - 2);

          // Add placeholder text
          ctx.fillStyle = "#888888";
          ctx.font = "16px Arial";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText("New Design", thumbWidth / 2, thumbHeight / 2);
        }

        // Convert to base64 data URL
        return canvas.toDataURL("image/png");
      } catch (error) {
        console.warn(
          "Failed to generate canvas thumbnail, using fallback:",
          error
        );
        return transparentPng;
      }
    }

    // Fallback for server-side or when canvas is not available
    return transparentPng;
  }
}

// Export singleton instance
export const designService = new DesignService();

// Export types and utilities
export { generateObjectId, generateRequestId } from "../utils/idGenerator";
